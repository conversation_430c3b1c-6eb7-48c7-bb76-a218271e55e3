// chain_i2v.js
// Node 18+
// Usage: node chain_i2v.js ./config.json

import fs from "fs";
import fsp from "fs/promises";
import path from "path";
import { exec as _exec } from "child_process";
import { fileURLToPath } from "url";
import axios from "axios";
import FormData from "form-data";
const exec = (cmd) =>
  new Promise((resolve, reject) =>
    _exec(cmd, (err, stdout, stderr) => (err ? reject(new Error(stderr || err.message)) : resolve({ stdout, stderr })))
  );

let WebSocket = null;
try { ({ default: WebSocket } = await import("ws")); } catch { /* optional */ }

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ---- config ----
const COMFY_BASE = process.env.COMFY_BASE || "http://127.0.0.1:8188";
const BASE_WORKFLOW_PATH = process.env.WORKFLOW_JSON || path.join(__dirname, "wan2.2_chain_base.json"); // put your uploaded file name here
const WORK_DIR = path.join(__dirname, "runs");

async function waitForHistory(promptId, { timeoutMs = 10 * 60 * 1000, pollMs = 1500 } = {}) {
  const started = Date.now();
  const spinner = ["|","/","-","\\"];
  let spinIdx = 0;
  let ws = null;
  let wsMsg = ""; // last progress line from WS

  // optional websocket live progress
  if (WebSocket) {
    try {
      ws = new WebSocket(COMFY_BASE.replace("http", "ws") + "/ws");
      ws.on("open", () => {
        // no handshake needed - ComfyUI streams all job events
      });
      ws.on("message", (buf) => {
        try {
          const msg = JSON.parse(buf.toString());
          // common messages: { type: "status", data: {...} }, { type: "progress", data:{ node_id, value, max } }, { type:"executing", data:{ node:{ id, type }, prompt_id } }
          if (msg?.type === "progress" && msg?.data?.max) {
            const pct = Math.floor((msg.data.value / msg.data.max) * 100);
            wsMsg = `progress: node ${msg.data.node_id} ${pct}%`;
          } else if (msg?.type === "executing" && msg?.data?.prompt_id === promptId) {
            const n = msg.data.node;
            wsMsg = `executing: ${n?.type || "node"} (${n?.id ?? "?"})`;
          } else if (msg?.type === "status") {
            // you could read queue/running counts here if you want
          }
        } catch { /* ignore malformed */ }
      });
      ws.on("error", () => {});
    } catch { ws = null; }
  }

  // render loop
  process.stdout.write("\n");
  while (true) {
    const res = await axios.get(`${COMFY_BASE}/history/${promptId}`).catch(() => ({ data: null }));
    const h = res.data?.[promptId];

    if (h?.status?.completed === true) {
      process.stdout.write(`\r✔ ComfyUI done in ${Math.round((Date.now()-started)/1000)}s${" ".repeat(40)}\n`);
      if (ws) try { ws.close(); } catch {}
      return h;
    }
    if (h?.status?.status_str === "error") {
      if (ws) try { ws.close(); } catch {}
      throw new Error(`ComfyUI job failed: ${JSON.stringify(h.status)}`);
    }

    const elapsed = Math.round((Date.now() - started) / 1000);
    const spin = spinner[spinIdx++ % spinner.length];
    const line = wsMsg ? `waiting ${spin}  ${wsMsg}  elapsed ${elapsed}s` : `waiting ${spin}  elapsed ${elapsed}s`;
    process.stdout.write(`\r${line}${" ".repeat(30)}`);

    if (Date.now() - started > timeoutMs) {
      if (ws) try { ws.close(); } catch {}
      throw new Error("Timed out waiting for ComfyUI history");
    }
    await sleep(pollMs);
  }
}


// utility
const sleep = (ms) => new Promise((r) => setTimeout(r, ms));
const ensureDir = async (p) => fsp.mkdir(p, { recursive: true });

async function uploadImageToComfy(localPath) {
  const form = new FormData();
  form.append("image", fs.createReadStream(localPath));
  // saves into ComfyUI/input
  const url = `${COMFY_BASE}/upload/image`;
  const res = await axios.post(url, form, { headers: form.getHeaders(), maxContentLength: Infinity, maxBodyLength: Infinity });
  // Response usually { name: "filename.png", subfolder: "", type: "input" }
  const name = res?.data?.name || path.basename(localPath);
  return name;
}

async function postPrompt(graph) {
  const res = await axios.post(`${COMFY_BASE}/prompt`, { prompt: graph });
  // returns { prompt_id, number }
  return res.data.prompt_id;
}

async function __waitForHistory(promptId, { timeoutMs = 10 * 60 * 1000, pollMs = 1500 } = {}) {
  const started = Date.now();
  while (true) {
    const res = await axios.get(`${COMFY_BASE}/history/${promptId}`);
    const h = res.data?.[promptId];
    if (h && h.status?.completed === true) return h;
    if (h && h.status?.status_str === "error") throw new Error(`ComfyUI job failed: ${JSON.stringify(h.status)}`);
    if (Date.now() - started > timeoutMs) throw new Error("Timed out waiting for ComfyUI history");
    await sleep(pollMs);
  }
}

function findVHSOutput(historyObj) {
  // scan outputs for any node that produced a video file
  // historyObj.outputs is keyed by node_id strings ("8", "23", etc.)
  for (const [nodeId, out] of Object.entries(historyObj.outputs || {})) {
    const files = out.files || out.images || [];
    // Comfy returns { filename, subfolder, type } for each file
    const video = files.find((f) => typeof f.filename === "string" && f.filename.toLowerCase().endsWith(".mp4"));
    if (video) return video;
  }
  return null;
}

async function downloadComfyFile(fileInfo, destPath) {
  // fileInfo: { filename, subfolder, type: "output" }
  const params = new URLSearchParams({
    filename: fileInfo.filename,
    subfolder: fileInfo.subfolder || "",
    folder: fileInfo.type || "output",
  });
  const url = `${COMFY_BASE}/view?${params.toString()}`;
  const res = await axios.get(url, { responseType: "arraybuffer" });
  await fsp.writeFile(destPath, Buffer.from(res.data));
}

function framesFromSeconds(seconds, fps) {
  return Math.max(1, Math.round(Number(seconds) * Number(fps)));
}

async function extractLastFrame(inVideoPath, outImagePath) {
  // relies on ffmpeg being installed
  // using -sseof -1 seeks to 1 second from end; with -vframes 1, you get the last frame reliably
  const cmd = `ffmpeg -y -sseof -1 -i "${inVideoPath}" -vframes 1 "${outImagePath}"`;
  await exec(cmd);
}

async function concatVideosLossless(segmentPaths, outputPath) {
  const listFile = path.join(path.dirname(outputPath), "concat_list.txt");
  const listText = segmentPaths.map((p) => `file '${p.replace(/'/g, "'\\''")}'`).join("\n");
  await fsp.writeFile(listFile, listText);
  // Use stream copy to keep original quality
  const cmd = `ffmpeg -y -f concat -safe 0 -i "${listFile}" -c copy "${outputPath}"`;
  await exec(cmd);
}

function patchGraphForSegment(baseGraph, {
  inputImageName,
  width,
  height,
  fps,
  frames,
  positivePrompt,
  negativePrompt,
  filenamePrefix,
}) {
  // deep clone
  const graph = JSON.parse(JSON.stringify(baseGraph));

  // Node IDs as in the provided workflow
  // 4: CLIPTextEncode (Negative)
  // 5: CLIPTextEncode (Positive)
  // 8: VHS_VideoCombine
  // 9: WanImageToVideo
  // 10: LoadImage

  if (!graph["4"] || !graph["5"] || !graph["8"] || !graph["9"] || !graph["10"]) {
    throw new Error("Expected nodes 4,5,8,9,10 not found in base workflow JSON");
  }

  graph["4"].inputs.text = negativePrompt || "";
  graph["5"].inputs.text = positivePrompt || "";

  graph["9"].inputs.width = Number(width);
  graph["9"].inputs.height = Number(height);
  graph["9"].inputs.length = Number(frames);

  graph["8"].inputs.frame_rate = Number(fps);
  graph["8"].inputs.filename_prefix = filenamePrefix;

  graph["10"].inputs.image = inputImageName;

  return graph;
}

async function main() {
  const cfgPath = process.argv[2];
  if (!cfgPath) {
    console.error("Usage: node chain_i2v.js ./config.json");
    process.exit(1);
  }

  await ensureDir(WORK_DIR);

  const cfgRaw = await fsp.readFile(cfgPath, "utf-8");
  const cfg = JSON.parse(cfgRaw);

  const {
    image,
    width,
    height,
    fps,
    prompt_chain = [],
    output = "final_output.mp4",
    segment_prefix = "segment"
  } = cfg;

  if (!image) throw new Error("config.image is required");

  const baseGraph = JSON.parse(await fsp.readFile(BASE_WORKFLOW_PATH, "utf-8"));

  console.log("Uploading initial image:", image);
  const firstImageName = await uploadImageToComfy(path.resolve(image));
  let currentStartImageName = firstImageName;

  const segmentVideos = [];

  for (let i = 0; i < prompt_chain.length; i++) {
    const seg = prompt_chain[i];
    const seconds = Number(seg.length || 5);
    const frameCount = framesFromSeconds(seconds, fps);

    console.log(`\n[Segment ${i + 1}/${prompt_chain.length}]`);
    console.log("  Positive prompt:", seg.positive_prompt);
    console.log("  Negative prompt:", seg.negative_prompt);
    console.log(`  Duration: ${seconds}s (${frameCount} frames @ ${fps} fps)`);

    const filenamePrefix = `${segment_prefix}_${String(i + 1).padStart(2, "0")}`;

    const graph = patchGraphForSegment(baseGraph, {
      inputImageName: currentStartImageName,
      width,
      height,
      fps,
      frames: frameCount,
      positivePrompt: seg.positive_prompt,
      negativePrompt: seg.negative_prompt,
      filenamePrefix
    });

    console.log("  Submitting to ComfyUI...");
    const promptId = await postPrompt(graph);

    console.log(`  Prompt ID: ${promptId}`);
    console.log("  Waiting for ComfyUI (live progress below)...");

    console.log("  Waiting for ComfyUI to finish (this may take a while)...");
    const hist = await waitForHistory(promptId);

    console.log("  Downloading video...");
    const vfile = findVHSOutput(hist);
    const segPath = path.join(WORK_DIR, `${filenamePrefix}.mp4`);
    await downloadComfyFile(vfile, segPath);
    console.log("  Saved:", segPath);
    segmentVideos.push(segPath);

    if (i < prompt_chain.length - 1) {
      console.log("  Extracting last frame for next segment...");
      const lastFramePath = path.join(WORK_DIR, `${filenamePrefix}_last.png`);
      await extractLastFrame(segPath, lastFramePath);
      const uploadedName = await uploadImageToComfy(lastFramePath);
      currentStartImageName = uploadedName;
    }
  }

  console.log("\nMerging all segments...");
  const outPath = path.resolve(output);
  await concatVideosLossless(segmentVideos, outPath);

  console.log("\n✅ All done. Final video:", outPath);
}

main().catch((err) => {
  console.error("❌ Error:", err);
  process.exit(1);
});